/**
 * Integrated App Builder
 * 
 * Main App Builder component that integrates all UI/UX improvements
 * with existing features including WebSocket collaboration, template system,
 * code export, tutorial assistant, and AI design suggestions.
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Layout, message, Spin, Alert, Modal, Button } from 'antd';
import { useSelector, useDispatch } from 'react-redux';
import styled from 'styled-components';

// Enhanced UI/UX Components - with fallbacks
let ResponsiveAppLayout, AccessibleComponent, EnhancedComponentPaletteFixed,
  UXEnhancedPropertyEditor, UXEnhancedPreviewArea, EnhancedKeyboardShortcuts, DragDropProvider;

// New Feature Components
let TestingTools, DataManagementTools, PerformanceTools, EnhancedCodeExporter, IntegratedTutorialAssistant;

try {
  ResponsiveAppLayout = require('../layout/ResponsiveAppLayout').default;
} catch (error) {
  console.warn('ResponsiveAppLayout not available, using fallback');
  ResponsiveAppLayout = ({ children, headerContent, leftPanel, rightPanel }) => (
    <Layout style={{ height: '100vh' }}>
      {headerContent && <Layout.Header style={{ background: '#fff', padding: '0 24px' }}>{headerContent}</Layout.Header>}
      <Layout>
        {leftPanel && <Layout.Sider width={250} style={{ background: '#fff' }}>{leftPanel}</Layout.Sider>}
        <Layout.Content style={{ padding: '24px' }}>{children}</Layout.Content>
        {rightPanel && <Layout.Sider width={250} style={{ background: '#fff' }}>{rightPanel}</Layout.Sider>}
      </Layout>
    </Layout>
  );
}

try {
  AccessibleComponent = require('../common/AccessibleComponent').default;
} catch (error) {
  console.warn('AccessibleComponent not available, using fallback');
  AccessibleComponent = ({ children, ...props }) => <div {...props}>{children}</div>;
}

try {
  EnhancedComponentPaletteFixed = require('./EnhancedComponentPaletteFixed').default;
} catch (error) {
  console.warn('EnhancedComponentPaletteFixed not available, using fallback');
  EnhancedComponentPaletteFixed = ({ onAddComponent }) => (
    <div style={{ padding: '16px' }}>
      <h3>Components</h3>
      <Button onClick={() => onAddComponent('button')} block style={{ marginBottom: '8px' }}>Add Button</Button>
      <Button onClick={() => onAddComponent('text')} block style={{ marginBottom: '8px' }}>Add Text</Button>
      <Button onClick={() => onAddComponent('input')} block>Add Input</Button>
    </div>
  );
}

try {
  UXEnhancedPropertyEditor = require('./UXEnhancedPropertyEditor').default;
} catch (error) {
  console.warn('UXEnhancedPropertyEditor not available, using fallback');
  UXEnhancedPropertyEditor = ({ component }) => (
    <div style={{ padding: '16px' }}>
      <h3>Properties</h3>
      {component ? (
        <div>
          <p><strong>Type:</strong> {component.type}</p>
          <p><strong>ID:</strong> {component.id}</p>
        </div>
      ) : (
        <p>Select a component to edit properties</p>
      )}
    </div>
  );
}

try {
  UXEnhancedPreviewArea = require('./UXEnhancedPreviewArea').default;
} catch (error) {
  console.warn('UXEnhancedPreviewArea not available, using fallback');
  UXEnhancedPreviewArea = ({ components, onSelectComponent, onDrop }) => (
    <div style={{
      minHeight: '400px',
      border: '2px dashed #ccc',
      borderRadius: '8px',
      padding: '20px',
      textAlign: 'center',
      background: '#fafafa'
    }}>
      {components.length === 0 ? (
        <div>
          <h3>Canvas Area</h3>
          <p>Drag components here to start building</p>
        </div>
      ) : (
        <div>
          <h3>Preview</h3>
          <p>{components.length} component(s) added</p>
        </div>
      )}
    </div>
  );
}

try {
  EnhancedKeyboardShortcuts = require('./EnhancedKeyboardShortcuts').default;
} catch (error) {
  console.warn('EnhancedKeyboardShortcuts not available, using fallback');
  EnhancedKeyboardShortcuts = () => null;
}

try {
  const dragDropModule = require('../dragdrop/EnhancedDragDropSystem');
  DragDropProvider = dragDropModule.DragDropProvider;
} catch (error) {
  console.warn('DragDropProvider not available, using fallback');
  DragDropProvider = ({ children }) => children;
}

// Progressive Loading - with fallback
let useProgressiveLoading, ProgressiveWrapper;
try {
  const progressiveModule = require('../../utils/progressiveLoading');
  useProgressiveLoading = progressiveModule.useProgressiveLoading;
  ProgressiveWrapper = progressiveModule.ProgressiveWrapper;
} catch (error) {
  console.warn('Progressive loading not available, using fallback');
  useProgressiveLoading = () => ({ isLoaded: true, loadComponent: () => { } });
  ProgressiveWrapper = ({ children }) => children;
}

// Lazy-loaded Feature Components - with fallbacks
let TutorialAssistant, AIDesignSuggestions, TemplateManager, CodeExporter, CollaborationIndicator;
try {
  const lazyComponents = require('../../config/lazyComponents');
  TutorialAssistant = lazyComponents.TutorialAssistant;
  AIDesignSuggestions = lazyComponents.AIDesignSuggestions;
  TemplateManager = lazyComponents.TemplateManager;
  CodeExporter = lazyComponents.CodeExporter;
  CollaborationIndicator = lazyComponents.CollaborationIndicator;
} catch (error) {
  console.warn('Lazy components not available, using fallbacks');
  const FallbackComponent = ({ children, ...props }) => (
    <div style={{ padding: '10px', border: '1px dashed #ccc', borderRadius: '4px' }}>
      Feature not available
    </div>
  );
  TutorialAssistant = FallbackComponent;
  AIDesignSuggestions = FallbackComponent;
  TemplateManager = FallbackComponent;
  CodeExporter = FallbackComponent;
  CollaborationIndicator = FallbackComponent;
}

// Import new components - with fallbacks
let AppBuilderExamples, IntegratedTutorialSystem, TestingTools, PerformanceMonitor;
try {
  AppBuilderExamples = require('../examples/AppBuilderExamples').default;
} catch (error) {
  console.warn('AppBuilderExamples not available');
  AppBuilderExamples = () => <div>Examples not available</div>;
}

try {
  IntegratedTutorialSystem = require('../tutorial/IntegratedTutorialSystem').default;
} catch (error) {
  console.warn('IntegratedTutorialSystem not available');
  IntegratedTutorialSystem = () => <div>Tutorial system not available</div>;
}

try {
  TestingTools = require('../enhanced/TestingTools').default;
} catch (error) {
  console.warn('TestingTools not available');
  TestingTools = () => <div>Testing tools not available</div>;
}

try {
  PerformanceMonitor = require('../enhanced/PerformanceMonitor').default;
} catch (error) {
  console.warn('PerformanceMonitor not available');
  PerformanceMonitor = () => <div>Performance monitor not available</div>;
}

// Design System - with fallbacks
let theme, visualHierarchy;
try {
  const designSystem = require('../../design-system');
  theme = designSystem.theme;
  visualHierarchy = designSystem.visualHierarchy;
} catch (error) {
  console.warn('Design system not available, using fallback theme');
  theme = {
    colors: { primary: '#1890ff', secondary: '#666' },
    spacing: { sm: '8px', md: '16px', lg: '24px' },
    borderRadius: { sm: '4px', md: '8px' }
  };
  visualHierarchy = {
    headings: { h1: { fontSize: '24px' }, h2: { fontSize: '20px' } }
  };
}

// Load new feature components with fallbacks
try {
  TestingTools = require('../testing/TestingTools').default;
} catch (error) {
  console.warn('TestingTools not available, using fallback');
  TestingTools = () => <div>Testing Tools not available</div>;
}

try {
  DataManagementTools = require('../data/DataManagementTools').default;
} catch (error) {
  console.warn('DataManagementTools not available, using fallback');
  DataManagementTools = () => <div>Data Management Tools not available</div>;
}

try {
  PerformanceTools = require('../performance/PerformanceTools').default;
} catch (error) {
  console.warn('PerformanceTools not available, using fallback');
  PerformanceTools = () => <div>Performance Tools not available</div>;
}

try {
  EnhancedCodeExporter = require('../export/EnhancedCodeExporter').default;
} catch (error) {
  console.warn('EnhancedCodeExporter not available, using fallback');
  EnhancedCodeExporter = () => <div>Enhanced Code Exporter not available</div>;
}

try {
  IntegratedTutorialAssistant = require('../tutorial/IntegratedTutorialAssistant').default;
} catch (error) {
  console.warn('IntegratedTutorialAssistant not available, using fallback');
  IntegratedTutorialAssistant = () => <div>Tutorial Assistant not available</div>;
}

// Hooks and Services - with fallbacks
let useWebSocket, useAppBuilder, useTutorial, useAIDesignSuggestions, useTemplates, useCodeExport, useCollaboration;

try {
  useWebSocket = require('../../hooks/useWebSocket').default;
} catch (error) {
  console.warn('useWebSocket not available, using fallback');
  useWebSocket = () => ({
    isConnected: false,
    collaborators: [],
    sendUpdate: () => { },
    sendCursor: () => { }
  });
}

try {
  useAppBuilder = require('../../hooks/useAppBuilder').default;
} catch (error) {
  console.warn('useAppBuilder not available, using fallback');
  useAppBuilder = () => ({
    components: [],
    addComponent: () => { },
    updateComponent: () => { },
    deleteComponent: () => { },
    moveComponent: () => { },
    duplicateComponent: () => { },
    undoAction: () => { },
    redoAction: () => { },
    canUndo: false,
    canRedo: false,
    saveProject: () => { },
    loadProject: () => { },
    isModified: false
  });
}

try {
  useTutorial = require('../../hooks/useTutorial').default;
} catch (error) {
  console.warn('useTutorial not available, using fallback');
  useTutorial = () => ({
    isActive: false,
    currentStep: 0,
    totalSteps: 0,
    nextStep: () => { },
    previousStep: () => { },
    skipTutorial: () => { },
    startTutorial: () => { }
  });
}

try {
  useAIDesignSuggestions = require('../../hooks/useAIDesignSuggestions').default;
} catch (error) {
  console.warn('useAIDesignSuggestions not available, using fallback');
  useAIDesignSuggestions = () => ({
    suggestions: [],
    loading: false,
    generateSuggestions: () => { },
    applySuggestion: () => { },
    dismissSuggestion: () => { }
  });
}

try {
  useTemplates = require('../../hooks/useTemplates').default;
} catch (error) {
  console.warn('useTemplates not available, using fallback');
  useTemplates = () => ({
    templates: [],
    loading: false,
    saveAsTemplate: () => { },
    loadTemplate: () => { },
    deleteTemplate: () => { }
  });
}

try {
  useCodeExport = require('../../hooks/useCodeExport').default;
} catch (error) {
  console.warn('useCodeExport not available, using fallback');
  useCodeExport = () => ({
    exportFormats: ['React', 'Vue', 'Angular'],
    loading: false,
    exportCode: () => { },
    downloadCode: () => { }
  });
}

try {
  useCollaboration = require('../../hooks/useCollaboration').default;
} catch (error) {
  console.warn('useCollaboration not available, using fallback');
  useCollaboration = () => ({
    activeUsers: [],
    comments: [],
    addComment: () => { },
    resolveComment: () => { }
  });
}

const IntegratedContainer = styled.div`
  height: 100vh;
  background: ${props => props.theme?.colors?.background?.default || '#f5f5f5'};
  position: relative;
  overflow: hidden;

  /* Ensure proper stacking context */
  z-index: 0;
`;

const HeaderContent = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme?.spacing?.[4] || '16px'};
  flex: 1;

  .app-title {
    font-size: ${props => props.theme?.visualHierarchy?.heading?.h4?.fontSize || '18px'};
    font-weight: ${props => props.theme?.visualHierarchy?.heading?.h4?.fontWeight || '600'};
    margin: 0;
    color: ${props => props.theme?.colors?.text?.primary || '#333'};
  }

  .project-name {
    font-size: ${props => props.theme?.visualHierarchy?.body?.small?.fontSize || '12px'};
    color: ${props => props.theme?.colors?.text?.secondary || '#666'};
    margin-left: ${props => props.theme?.spacing?.[2] || '8px'};
  }
`;

const FeatureToggles = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme?.spacing?.[2] || '8px'};

  @media (max-width: 768px) {
    gap: ${props => props.theme?.spacing?.[1] || '4px'};
  }
`;

const LoadingOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: ${props => props.theme?.zIndex?.modal || 1000};

  .loading-text {
    margin-top: ${props => props.theme?.spacing?.[4] || '16px'};
    font-size: ${props => props.theme?.visualHierarchy?.body?.medium?.fontSize || '14px'};
    color: ${props => props.theme?.colors?.text?.secondary || '#666'};
  }
`;

const ErrorBoundary = styled.div`
  padding: ${props => props.theme?.spacing?.[8] || '32px'};
  text-align: center;

  .error-title {
    font-size: ${props => props.theme?.visualHierarchy?.heading?.h3?.fontSize || '20px'};
    font-weight: ${props => props.theme?.visualHierarchy?.heading?.h3?.fontWeight || '600'};
    color: ${props => props.theme?.colors?.error?.main || '#ff4d4f'};
    margin-bottom: ${props => props.theme?.spacing?.[4] || '16px'};
  }

  .error-message {
    font-size: ${props => props.theme?.visualHierarchy?.body?.medium?.fontSize || '14px'};
    color: ${props => props.theme?.colors?.text?.secondary || '#666'};
    margin-bottom: ${props => props.theme?.spacing?.[6] || '24px'};
  }
`;

export default function IntegratedAppBuilder({
  projectId,
  initialComponents = [],
  enableFeatures = {
    websocket: true,
    tutorial: true,
    aiSuggestions: true,
    templates: true,
    codeExport: true,
    collaboration: true,
    testing: true,
    dataManagement: true,
    performanceMonitoring: true,
    enhancedExport: true,
    tutorialAssistant: true,
  },
  onSave,
  onLoad,
  onError,
}) {
  // Redux state
  const dispatch = useDispatch();
  const user = useSelector(state => state.auth?.user);
  const project = useSelector(state => state.projects?.current);

  // Local state
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedComponent, setSelectedComponent] = useState(null);
  const [previewMode, setPreviewMode] = useState(false);

  // Clipboard for copy/paste operations
  const [clipboard, setClipboard] = useState(null);

  // New feature states
  const [activeFeaturePanel, setActiveFeaturePanel] = useState(null);
  const [testResults, setTestResults] = useState({});
  const [performanceData, setPerformanceData] = useState({
    coreWebVitals: {},
    bundleSize: { total: 0 },
    renderMetrics: {},
    memoryUsage: { used: 0, total: 100 }
  });
  const [dataBindings, setDataBindings] = useState([]);
  const [exportSettings, setExportSettings] = useState({
    framework: 'react',
    typescript: true,
    includeTests: false,
    includeStyles: true,
    codeQuality: {
      prettier: true,
      eslint: true,
      components: true
    }
  });
  const [tutorialProgress, setTutorialProgress] = useState({
    currentTutorial: null,
    completedTutorials: new Set(),
    isActive: false
  });

  // Feature states
  const [showTutorial, setShowTutorial] = useState(false);
  const [showAISuggestions, setShowAISuggestions] = useState(false);
  const [showTemplates, setShowTemplates] = useState(false);
  const [showCodeExport, setShowCodeExport] = useState(false);
  const [showExamples, setShowExamples] = useState(false);
  const [showTestingTools, setShowTestingTools] = useState(false);
  const [showPerformanceMonitor, setShowPerformanceMonitor] = useState(false);

  // Progressive loading for feature components
  const progressiveLoading = useProgressiveLoading({
    strategy: 'priority',
    features: Object.keys(enableFeatures).filter(key => enableFeatures[key]),
    autoStart: true,
    delay: 1000 // Start loading after initial render
  });

  // App Builder hook with enhanced features
  const {
    components,
    addComponent,
    updateComponent,
    deleteComponent,
    moveComponent,
    duplicateComponent,
    undoAction,
    redoAction,
    canUndo,
    canRedo,
    saveProject,
    loadProject,
    isModified,
  } = useAppBuilder({
    projectId,
    initialComponents,
    autoSave: true,
    onSave,
    onLoad,
    onError: (err) => {
      setError(err);
      if (onError) onError(err);
    },
  });

  // WebSocket collaboration
  const {
    isConnected: websocketConnected,
    collaborators,
    sendUpdate,
    sendCursor,
  } = useWebSocket({
    enabled: enableFeatures.websocket,
    projectId,
    userId: user?.id,
    onComponentUpdate: updateComponent,
    onComponentAdd: addComponent,
    onComponentDelete: deleteComponent,
    onComponentMove: moveComponent,
  });

  // Tutorial system
  const {
    isActive: tutorialActive,
    currentStep,
    totalSteps,
    nextStep,
    previousStep,
    skipTutorial,
    startTutorial,
  } = useTutorial({
    enabled: enableFeatures.tutorial,
    autoStart: !user?.hasCompletedTutorial,
  });

  // AI Design Suggestions
  const {
    suggestions,
    loading: aiLoading,
    generateSuggestions,
    applySuggestion,
    dismissSuggestion,
  } = useAIDesignSuggestions({
    enabled: enableFeatures.aiSuggestions,
    components,
    selectedComponent,
    autoRefresh: true,
  });

  // Template system
  const {
    templates,
    loading: templatesLoading,
    saveAsTemplate,
    loadTemplate,
    deleteTemplate,
  } = useTemplates({
    enabled: enableFeatures.templates,
    projectId,
  });

  // Code export
  const {
    exportCode,
    exportFormats,
    loading: exportLoading,
    downloadCode,
  } = useCodeExport({
    enabled: enableFeatures.codeExport,
    components,
    projectSettings: project?.settings,
  });

  // Collaboration features
  const {
    activeUsers,
    comments,
    addComment,
    resolveComment,
    shareProject,
  } = useCollaboration({
    enabled: enableFeatures.collaboration,
    projectId,
    websocketConnected,
  });

  // Initialize app
  useEffect(() => {
    const initializeApp = async () => {
      try {
        setLoading(true);

        if (projectId) {
          await loadProject(projectId);
        }

        // Initialize features
        if (enableFeatures.aiSuggestions) {
          await generateSuggestions();
        }

        setLoading(false);
      } catch (err) {
        setError(err);
        setLoading(false);
      }
    };

    initializeApp();
  }, [projectId, enableFeatures.aiSuggestions]); // Removed function dependencies to prevent infinite re-renders

  // Handle incoming WebSocket events for new features
  useEffect(() => {
    if (!websocketConnected) return;

    const handleWebSocketMessage = (event) => {
      try {
        const data = JSON.parse(event.data);

        switch (data.type) {
          case 'test_results':
            if (data.userId !== user?.id) {
              message.info(`${data.userId} completed ${data.testType} tests: ${data.results.passed}/${data.results.total} passed`);
            }
            break;

          case 'optimization_applied':
            if (data.userId !== user?.id) {
              message.info(`${data.userId} applied optimization: ${data.suggestion}`);
            }
            break;

          case 'data_binding_created':
            if (data.userId !== user?.id) {
              setDataBindings(prev => [...prev, data.binding]);
              message.info(`${data.userId} created a data binding`);
            }
            break;

          case 'data_binding_updated':
            if (data.userId !== user?.id) {
              setDataBindings(prev => prev.map(b => b.id === data.binding.id ? data.binding : b));
              message.info(`${data.userId} updated a data binding`);
            }
            break;

          case 'data_binding_deleted':
            if (data.userId !== user?.id) {
              setDataBindings(prev => prev.filter(b => b.id !== data.bindingId));
              message.info(`${data.userId} deleted a data binding`);
            }
            break;

          case 'code_exported':
            if (data.userId !== user?.id) {
              message.info(`${data.userId} exported code for ${data.framework}`);
            }
            break;

          case 'tutorial_completed':
            if (data.userId !== user?.id) {
              message.info(`${data.userId} completed a tutorial`);
            }
            break;

          default:
            // Handle other WebSocket events
            break;
        }
      } catch (error) {
        console.warn('Failed to parse WebSocket message:', error);
      }
    };

    // Note: WebSocket message handling is integrated through the useWebSocket hook
    console.log('WebSocket event handling for new features is ready');

    return () => {
      // Cleanup if needed
    };
  }, [websocketConnected, user?.id]);

  // Persist feature panel state and settings
  useEffect(() => {
    const savedSettings = localStorage.getItem(`app-builder-settings-${projectId}`);
    if (savedSettings) {
      try {
        const settings = JSON.parse(savedSettings);
        if (settings.exportSettings) {
          setExportSettings(prev => ({ ...prev, ...settings.exportSettings }));
        }
        if (settings.dataBindings) {
          setDataBindings(settings.dataBindings);
        }
        if (settings.tutorialProgress) {
          setTutorialProgress(prev => ({ ...prev, ...settings.tutorialProgress }));
        }
      } catch (error) {
        console.warn('Failed to load saved settings:', error);
      }
    }
  }, [projectId]);

  // Save settings when they change
  useEffect(() => {
    const settings = {
      exportSettings,
      dataBindings,
      tutorialProgress,
      lastUpdated: Date.now()
    };

    localStorage.setItem(`app-builder-settings-${projectId}`, JSON.stringify(settings));
  }, [projectId, exportSettings, dataBindings, tutorialProgress]);

  // Handle component selection with collaboration
  const handleSelectComponent = useCallback((component) => {
    setSelectedComponent(component);

    // Send cursor position for collaboration
    if (websocketConnected && component) {
      sendCursor({
        componentId: component.id,
        action: 'select',
        timestamp: Date.now(),
      });
    }
  }, [websocketConnected, sendCursor]);

  // Handle component updates with real-time sync
  const handleUpdateComponent = useCallback((componentId, updates) => {
    updateComponent(componentId, updates);

    // Send update to collaborators
    if (websocketConnected) {
      sendUpdate({
        type: 'component_update',
        componentId,
        updates,
        userId: user?.id,
        timestamp: Date.now(),
      });
    }
  }, [updateComponent, websocketConnected, sendUpdate, user?.id]);

  // Handle component addition with AI suggestions
  const handleAddComponent = useCallback(async (componentType, position) => {
    const newComponent = await addComponent(componentType, position);

    // Generate AI suggestions for the new component
    if (enableFeatures.aiSuggestions && newComponent) {
      setTimeout(() => generateSuggestions(), 500);
    }

    // Send update to collaborators
    if (websocketConnected) {
      sendUpdate({
        type: 'component_add',
        component: newComponent,
        userId: user?.id,
        timestamp: Date.now(),
      });
    }

    return newComponent;
  }, [addComponent, enableFeatures.aiSuggestions, generateSuggestions, websocketConnected, sendUpdate, user?.id]);

  // Handle component deletion with confirmation
  const handleDeleteComponent = useCallback((componentId) => {
    const component = components.find(c => c.id === componentId);

    if (component) {
      deleteComponent(componentId);

      // Clear selection if deleted component was selected
      if (selectedComponent?.id === componentId) {
        setSelectedComponent(null);
      }

      // Send update to collaborators
      if (websocketConnected) {
        sendUpdate({
          type: 'component_delete',
          componentId,
          userId: user?.id,
          timestamp: Date.now(),
        });
      }

      message.success(`Deleted ${component.type} component`);
    }
  }, [components, deleteComponent, selectedComponent, websocketConnected, sendUpdate, user?.id]);

  // Enhanced keyboard shortcut handlers
  const handleKeyboardAction = useCallback((action, shortcutKey) => {
    switch (action) {
      case 'save':
        if (isModified) {
          saveProject();
          message.success('Project saved successfully');
        }
        break;

      case 'copy':
        if (selectedComponent) {
          setClipboard({ ...selectedComponent, id: undefined });
          message.success('Component copied to clipboard');
        }
        break;

      case 'paste':
        if (clipboard) {
          const newComponent = {
            ...clipboard,
            id: `component_${Date.now()}`,
            position: {
              x: (clipboard.position?.x || 0) + 20,
              y: (clipboard.position?.y || 0) + 20
            }
          };
          handleAddComponent(newComponent.type, newComponent.position);
          message.success('Component pasted from clipboard');
        }
        break;

      case 'cut':
        if (selectedComponent) {
          setClipboard({ ...selectedComponent, id: undefined });
          handleDeleteComponent(selectedComponent.id);
          message.success('Component cut to clipboard');
        }
        break;

      case 'undo':
        if (canUndo) {
          undoAction();
          message.success('Action undone');
        }
        break;

      case 'redo':
        if (canRedo) {
          redoAction();
          message.success('Action redone');
        }
        break;

      case 'delete':
        if (selectedComponent) {
          handleDeleteComponent(selectedComponent.id);
        }
        break;

      case 'preview':
        setPreviewMode(prev => !prev);
        message.success(`Preview mode ${!previewMode ? 'enabled' : 'disabled'}`);
        break;

      case 'new':
        handleAddComponent('text', { x: 100, y: 100 });
        break;

      case 'fullscreen':
        if (document.fullscreenElement) {
          document.exitFullscreen();
        } else {
          document.documentElement.requestFullscreen();
        }
        break;

      // New feature shortcuts
      case 'toggle_testing':
        if (enableFeatures.testing) {
          handleFeaturePanelToggle('testing');
          message.info('Testing panel toggled');
        }
        break;

      case 'toggle_performance':
        if (enableFeatures.performanceMonitoring) {
          handleFeaturePanelToggle('performance');
          message.info('Performance panel toggled');
        }
        break;

      case 'toggle_data':
        if (enableFeatures.dataManagement) {
          handleFeaturePanelToggle('data');
          message.info('Data management panel toggled');
        }
        break;

      case 'toggle_export':
        if (enableFeatures.enhancedExport) {
          handleFeaturePanelToggle('export');
          message.info('Export panel toggled');
        }
        break;

      case 'close_panels':
        if (activeFeaturePanel) {
          setActiveFeaturePanel(null);
          message.info('All panels closed');
        }
        break;

      default:
        console.log(`Unhandled keyboard action: ${action}`);
    }
  }, [
    selectedComponent,
    clipboard,
    isModified,
    canUndo,
    canRedo,
    previewMode,
    saveProject,
    undoAction,
    redoAction,
    handleAddComponent,
    handleDeleteComponent,
    enableFeatures,
    handleFeaturePanelToggle,
    activeFeaturePanel,
    setActiveFeaturePanel,
  ]);

  // New Feature Handlers

  // Testing Tools Handlers
  const handleTestComplete = useCallback((testType, results) => {
    setTestResults(prev => ({ ...prev, [testType]: results }));

    const passedTests = results.filter(r => r.status === 'passed').length;
    const totalTests = results.length;

    message.success(`${testType} tests completed: ${passedTests}/${totalTests} passed`);

    // Send test results via WebSocket for collaboration
    if (websocketConnected && sendUpdate) {
      sendUpdate({
        type: 'test_results',
        testType,
        results: { passed: passedTests, total: totalTests },
        userId: user?.id,
        timestamp: Date.now()
      });
    }
  }, [websocketConnected, sendUpdate, user?.id]);

  const handleTestStart = useCallback((testType) => {
    message.info(`Starting ${testType} tests...`);
    setTestResults(prev => ({ ...prev, [testType]: [] }));
  }, []);

  // Performance Monitoring Handlers
  const handleOptimizationApply = useCallback((suggestion) => {
    message.success(`Applied optimization: ${suggestion.title}`);

    setPerformanceData(prev => ({
      ...prev,
      optimizations: [...(prev.optimizations || []), {
        ...suggestion,
        appliedAt: Date.now()
      }]
    }));

    if (websocketConnected && sendUpdate) {
      sendUpdate({
        type: 'optimization_applied',
        suggestion: suggestion.title,
        userId: user?.id,
        timestamp: Date.now()
      });
    }
  }, [websocketConnected, sendUpdate, user?.id]);

  // Data Management Handlers
  const handleDataChange = useCallback((data) => {
    message.success('Data updated successfully');

    if (selectedComponent && data.componentId === selectedComponent.id) {
      handleUpdateComponent(selectedComponent.id, { data });
    }
  }, [selectedComponent, handleUpdateComponent]);

  const handleBindingCreate = useCallback((binding) => {
    setDataBindings(prev => [...prev, binding]);
    message.success('Data binding created');

    if (websocketConnected && sendUpdate) {
      sendUpdate({
        type: 'data_binding_created',
        binding,
        userId: user?.id,
        timestamp: Date.now()
      });
    }
  }, [websocketConnected, sendUpdate, user?.id]);

  const handleBindingUpdate = useCallback((binding) => {
    setDataBindings(prev => prev.map(b => b.id === binding.id ? binding : b));
    message.success('Data binding updated');

    if (websocketConnected && sendUpdate) {
      sendUpdate({
        type: 'data_binding_updated',
        binding,
        userId: user?.id,
        timestamp: Date.now()
      });
    }
  }, [websocketConnected, sendUpdate, user?.id]);

  const handleBindingDelete = useCallback((bindingId) => {
    setDataBindings(prev => prev.filter(b => b.id !== bindingId));
    message.success('Data binding deleted');

    if (websocketConnected && sendUpdate) {
      sendUpdate({
        type: 'data_binding_deleted',
        bindingId,
        userId: user?.id,
        timestamp: Date.now()
      });
    }
  }, [websocketConnected, sendUpdate, user?.id]);

  // Enhanced Export Handlers
  const handleEnhancedExport = useCallback((exportData) => {
    message.success(`Code exported for ${exportData.framework}`);

    setExportSettings(prev => ({
      ...prev,
      lastExport: {
        framework: exportData.framework,
        timestamp: Date.now(),
        settings: exportData.settings
      }
    }));

    if (websocketConnected && sendUpdate) {
      sendUpdate({
        type: 'code_exported',
        framework: exportData.framework,
        userId: user?.id,
        timestamp: Date.now()
      });
    }
  }, [websocketConnected, sendUpdate, user?.id]);

  const handleCodePreview = useCallback((code) => {
    message.info('Code preview generated');
  }, []);

  // Tutorial Assistant Handlers
  const handleTutorialComplete = useCallback((tutorial) => {
    setTutorialProgress(prev => ({
      ...prev,
      completedTutorials: new Set([...prev.completedTutorials, tutorial.id]),
      currentTutorial: null,
      isActive: false
    }));

    message.success(`Tutorial "${tutorial.title}" completed! 🎉`);

    if (websocketConnected && sendUpdate) {
      sendUpdate({
        type: 'tutorial_completed',
        tutorialId: tutorial.id,
        userId: user?.id,
        timestamp: Date.now()
      });
    }
  }, [websocketConnected, sendUpdate, user?.id]);

  const handleTutorialSkip = useCallback((tutorial) => {
    setTutorialProgress(prev => ({
      ...prev,
      currentTutorial: null,
      isActive: false
    }));

    message.info(`Tutorial "${tutorial.title}" skipped.`);
  }, []);

  // Feature Panel Management
  const handleFeaturePanelToggle = useCallback((panelType) => {
    setActiveFeaturePanel(prev => prev === panelType ? null : panelType);
  }, []);

  // Memoized header content
  const headerContent = useMemo(() => (
    <HeaderContent>
      <div>
        <h1 className="app-title">App Builder</h1>
        {project?.name && (
          <span className="project-name">{project.name}</span>
        )}

        {/* Status Indicators */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          marginTop: '4px',
          fontSize: '12px'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '4px',
            padding: '2px 8px',
            borderRadius: '12px',
            background: websocketConnected ? '#f6ffed' : '#fff2f0',
            border: `1px solid ${websocketConnected ? '#b7eb8f' : '#ffccc7'}`,
            color: websocketConnected ? '#52c41a' : '#ff4d4f'
          }}>
            <div style={{
              width: '6px',
              height: '6px',
              borderRadius: '50%',
              background: websocketConnected ? '#52c41a' : '#ff4d4f'
            }} />
            {websocketConnected ? 'Connected' : 'Offline'}
          </div>

          {isModified && (
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '4px',
              padding: '2px 8px',
              borderRadius: '12px',
              background: '#fff7e6',
              border: '1px solid #ffd591',
              color: '#fa8c16'
            }}>
              <div style={{
                width: '6px',
                height: '6px',
                borderRadius: '50%',
                background: '#fa8c16'
              }} />
              Unsaved Changes
            </div>
          )}

          {/* Active Feature Panel Indicator */}
          {activeFeaturePanel && (
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '4px',
              padding: '2px 8px',
              borderRadius: '12px',
              background: '#e6f7ff',
              border: '1px solid #91d5ff',
              color: '#1890ff'
            }}>
              <div style={{
                width: '6px',
                height: '6px',
                borderRadius: '50%',
                background: '#1890ff'
              }} />
              {activeFeaturePanel.charAt(0).toUpperCase() + activeFeaturePanel.slice(1)} Panel
            </div>
          )}

          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '4px',
            padding: '2px 8px',
            borderRadius: '12px',
            background: '#f0f5ff',
            border: '1px solid #adc6ff',
            color: '#1890ff'
          }}>
            {components.length} Component{components.length !== 1 ? 's' : ''}
          </div>
        </div>
      </div>

      <FeatureToggles>
        {enableFeatures.collaboration && (
          <ProgressiveWrapper
            componentName="CollaborationIndicator"
            strategy="viewport"
            fallback={<div style={{ width: 32, height: 32 }} />}
          >
            <CollaborationIndicator
              connected={websocketConnected}
              collaborators={collaborators}
              activeUsers={activeUsers}
            />
          </ProgressiveWrapper>
        )}

        {enableFeatures.aiSuggestions && (
          <ProgressiveWrapper
            componentName="AIDesignSuggestions"
            strategy="interaction"
            fallback={<div style={{ width: 120, height: 32 }} />}
          >
            <AIDesignSuggestions
              suggestions={suggestions}
              loading={aiLoading}
              onApply={applySuggestion}
              onDismiss={dismissSuggestion}
              compact
            />
          </ProgressiveWrapper>
        )}

        {enableFeatures.templates && (
          <div data-tutorial="theme-manager">
            <ProgressiveWrapper
              componentName="TemplateManager"
              strategy="interaction"
              fallback={<div style={{ width: 100, height: 32 }} />}
            >
              <TemplateManager
                templates={templates}
                loading={templatesLoading}
                onSave={saveAsTemplate}
                onLoad={loadTemplate}
                onDelete={deleteTemplate}
                compact
              />
            </ProgressiveWrapper>
          </div>
        )}

        {enableFeatures.codeExport && (
          <ProgressiveWrapper
            componentName="CodeExporter"
            strategy="interaction"
            fallback={<div style={{ width: 100, height: 32 }} />}
          >
            <CodeExporter
              formats={exportFormats}
              loading={exportLoading}
              onExport={exportCode}
              onDownload={downloadCode}
              compact
            />
          </ProgressiveWrapper>
        )}

        {/* Preview Mode Toggle */}
        <button
          data-tutorial="preview-mode"
          onClick={() => setPreviewMode(prev => !prev)}
          style={{
            background: previewMode ? '#52c41a' : '#f0f0f0',
            color: previewMode ? 'white' : '#333',
            border: 'none',
            borderRadius: '6px',
            padding: '8px 16px',
            cursor: 'pointer',
            fontSize: '14px',
            fontWeight: '500',
            display: 'flex',
            alignItems: 'center',
            gap: '6px',
            transition: 'all 0.2s ease',
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
          }}
          title={previewMode ? 'Exit preview mode' : 'Enter preview mode'}
        >
          👁️ {previewMode ? 'Exit Preview' : 'Preview'}
        </button>

        {/* Tutorial Button */}
        {enableFeatures.tutorial && (
          <button
            onClick={() => startTutorial()}
            style={{
              background: tutorialActive ?
                'linear-gradient(135deg, #52c41a 0%, #389e0d 100%)' :
                'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              padding: '8px 16px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500',
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
              transition: 'all 0.2s ease',
              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
            }}
            onMouseOver={(e) => {
              e.target.style.transform = 'translateY(-1px)';
              e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.15)';
            }}
            onMouseOut={(e) => {
              e.target.style.transform = 'translateY(0)';
              e.target.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
            }}
            title={tutorialActive ? 'Tutorial is active' : 'Start interactive tutorial'}
          >
            🎓 {tutorialActive ? 'Tutorial Active' : 'Start Tutorial'}
          </button>
        )}

        {/* Testing Tools Button */}
        {enableFeatures.testing && (
          <button
            data-tutorial="testing-tools"
            onClick={() => handleFeaturePanelToggle('testing')}
            style={{
              background: activeFeaturePanel === 'testing' ?
                'linear-gradient(135deg, #52c41a 0%, #389e0d 100%)' :
                'linear-gradient(135deg, #fa8c16 0%, #d46b08 100%)',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              padding: '8px 16px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500',
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
              transition: 'all 0.2s ease',
              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
            }}
            onMouseOver={(e) => {
              e.target.style.transform = 'translateY(-1px)';
              e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.15)';
            }}
            onMouseOut={(e) => {
              e.target.style.transform = 'translateY(0)';
              e.target.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
            }}
            title="Open testing and debugging tools"
          >
            🧪 {activeFeaturePanel === 'testing' ? 'Close Testing' : 'Testing'}
          </button>
        )}

        {/* Performance Monitor Button */}
        {enableFeatures.performanceMonitoring && (
          <button
            data-tutorial="performance-monitor"
            onClick={() => handleFeaturePanelToggle('performance')}
            style={{
              background: activeFeaturePanel === 'performance' ?
                'linear-gradient(135deg, #52c41a 0%, #389e0d 100%)' :
                'linear-gradient(135deg, #722ed1 0%, #531dab 100%)',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              padding: '8px 16px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500',
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
              transition: 'all 0.2s ease',
              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
            }}
            onMouseOver={(e) => {
              e.target.style.transform = 'translateY(-1px)';
              e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.15)';
            }}
            onMouseOut={(e) => {
              e.target.style.transform = 'translateY(0)';
              e.target.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
            }}
            title="Open performance monitoring dashboard"
          >
            ⚡ {activeFeaturePanel === 'performance' ? 'Close Performance' : 'Performance'}
          </button>
        )}

        {/* Data Management Button */}
        {enableFeatures.dataManagement && (
          <button
            data-tutorial="data-management"
            onClick={() => handleFeaturePanelToggle('data')}
            style={{
              background: activeFeaturePanel === 'data' ?
                'linear-gradient(135deg, #52c41a 0%, #389e0d 100%)' :
                'linear-gradient(135deg, #13c2c2 0%, #08979c 100%)',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              padding: '8px 16px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500',
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
              transition: 'all 0.2s ease',
              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
            }}
            onMouseOver={(e) => {
              e.target.style.transform = 'translateY(-1px)';
              e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.15)';
            }}
            onMouseOut={(e) => {
              e.target.style.transform = 'translateY(0)';
              e.target.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
            }}
            title="Open data management tools"
          >
            📊 {activeFeaturePanel === 'data' ? 'Close Data' : 'Data'}
          </button>
        )}

        {/* Enhanced Export Button */}
        {enableFeatures.enhancedExport && (
          <button
            data-tutorial="code-export"
            onClick={() => handleFeaturePanelToggle('export')}
            style={{
              background: activeFeaturePanel === 'export' ?
                'linear-gradient(135deg, #52c41a 0%, #389e0d 100%)' :
                'linear-gradient(135deg, #eb2f96 0%, #c41d7f 100%)',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              padding: '8px 16px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500',
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
              transition: 'all 0.2s ease',
              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
            }}
            onMouseOver={(e) => {
              e.target.style.transform = 'translateY(-1px)';
              e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.15)';
            }}
            onMouseOut={(e) => {
              e.target.style.transform = 'translateY(0)';
              e.target.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
            }}
            title="Enhanced code export with multiple frameworks"
          >
            💻 {activeFeaturePanel === 'export' ? 'Close Export' : 'Export'}
          </button>
        )}

        {/* Examples Button */}
        <button
          onClick={() => setShowExamples(true)}
          style={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            padding: '8px 16px',
            cursor: 'pointer',
            fontSize: '14px',
            fontWeight: '500',
            display: 'flex',
            alignItems: 'center',
            gap: '6px',
            transition: 'all 0.2s ease',
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
          }}
          onMouseOver={(e) => {
            e.target.style.transform = 'translateY(-1px)';
            e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.15)';
          }}
          onMouseOut={(e) => {
            e.target.style.transform = 'translateY(0)';
            e.target.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
          }}
          title="View examples and tutorials"
        >
          📚 Examples
        </button>
      </FeatureToggles>
    </HeaderContent>
  ), [
    project?.name,
    enableFeatures,
    websocketConnected,
    collaborators,
    activeUsers,
    suggestions,
    aiLoading,
    applySuggestion,
    dismissSuggestion,
    templates,
    templatesLoading,
    saveAsTemplate,
    loadTemplate,
    deleteTemplate,
    exportFormats,
    exportLoading,
    exportCode,
    downloadCode,
    setShowExamples,
    previewMode,
    setPreviewMode,
    components.length,
    isModified,
    tutorialActive,
    startTutorial,
    activeFeaturePanel,
    handleFeaturePanelToggle,
  ]);

  // Error boundary
  if (error) {
    return (
      <IntegratedContainer>
        <ErrorBoundary>
          <div className="error-title">Something went wrong</div>
          <div className="error-message">{error.message}</div>
          <button onClick={() => window.location.reload()}>
            Reload Application
          </button>
        </ErrorBoundary>
      </IntegratedContainer>
    );
  }

  return (
    <IntegratedContainer>
      <DragDropProvider showOverlay={true}>
        <AccessibleComponent
          role="application"
          ariaLabel="App Builder application for creating user interfaces"
        >
          <ResponsiveAppLayout
            headerContent={headerContent}
            leftPanel={
              <div data-tutorial="component-palette">
                <EnhancedComponentPaletteFixed
                  onAddComponent={handleAddComponent}
                  selectedComponent={selectedComponent}
                  showAISuggestions={enableFeatures.aiSuggestions}
                  loading={loading}
                />
              </div>
            }
            rightPanel={
              <div data-tutorial="property-editor">
                <UXEnhancedPropertyEditor
                  component={selectedComponent}
                  onUpdateComponent={handleUpdateComponent}
                  loading={loading}
                />
              </div>
            }
            showBreakpointIndicator={process.env.NODE_ENV === 'development'}
            enablePanelResize={true}
            persistLayout={true}
          >
            <div data-tutorial="canvas-area">
              <UXEnhancedPreviewArea
                components={components}
                selectedComponentId={selectedComponent?.id}
                onSelectComponent={handleSelectComponent}
                onDeleteComponent={handleDeleteComponent}
                onUpdateComponent={handleUpdateComponent}
                onMoveComponent={moveComponent}
                previewMode={previewMode}
                websocketConnected={websocketConnected}
                loading={loading}
                onDrop={handleAddComponent}
              />
            </div>
          </ResponsiveAppLayout>
        </AccessibleComponent>
      </DragDropProvider>

      {/* Enhanced Keyboard Shortcuts */}
      <EnhancedKeyboardShortcuts
        onAction={handleKeyboardAction}
        showQuickActions={true}
        enableCustomization={true}
        showFeedback={true}
      />

      {/* Tutorial Overlay */}
      {enableFeatures.tutorial && tutorialActive && (
        <TutorialAssistant
          currentStep={currentStep}
          totalSteps={totalSteps}
          onNext={nextStep}
          onPrevious={previousStep}
          onSkip={skipTutorial}
          onComplete={skipTutorial}
        />
      )}

      {/* Integrated Tutorial System */}
      {enableFeatures.tutorial && (
        <IntegratedTutorialSystem
          onTutorialComplete={() => {
            message.success('Tutorial completed! You\'re ready to build amazing apps.');
          }}
        >
          {/* This wraps the entire app for tutorial overlays */}
        </IntegratedTutorialSystem>
      )}

      {/* Enhanced Tutorial Assistant */}
      {enableFeatures.tutorialAssistant && (
        <IntegratedTutorialAssistant
          enableAutoStart={!user?.hasCompletedTutorial}
          showContextualHelp={true}
          onTutorialComplete={handleTutorialComplete}
          onTutorialSkip={handleTutorialSkip}
          features={Object.keys(enableFeatures).filter(key => enableFeatures[key])}
        />
      )}

      {/* Examples Modal */}
      {showExamples && (
        <Modal
          title="App Builder Examples & Tutorials"
          visible={showExamples}
          onCancel={() => setShowExamples(false)}
          footer={null}
          width="90%"
          style={{ top: 20 }}
          bodyStyle={{ padding: 0, height: '80vh', overflow: 'auto' }}
        >
          <AppBuilderExamples />
        </Modal>
      )}

      {/* Feature Panels with Error Boundaries */}
      {activeFeaturePanel === 'testing' && enableFeatures.testing && (
        <Modal
          title="Testing Tools"
          open={true}
          onCancel={() => setActiveFeaturePanel(null)}
          footer={null}
          width="90%"
          style={{ top: 20 }}
          bodyStyle={{ padding: 0, height: '80vh', overflow: 'auto' }}
        >
          <div style={{ padding: '16px' }}>
            <TestingTools
              components={components}
              onTestComplete={handleTestComplete}
              onTestStart={handleTestStart}
              enabledTests={['component', 'layout', 'accessibility', 'performance']}
              autoRun={false}
              showMetrics={true}
            />
          </div>
        </Modal>
      )}

      {activeFeaturePanel === 'performance' && enableFeatures.performanceMonitoring && (
        <Modal
          title="Performance Monitor"
          open={true}
          onCancel={() => setActiveFeaturePanel(null)}
          footer={null}
          width="90%"
          style={{ top: 20 }}
          bodyStyle={{ padding: 0, height: '80vh', overflow: 'auto' }}
        >
          <div style={{ padding: '16px' }}>
            <PerformanceTools
              components={components}
              onOptimizationApply={handleOptimizationApply}
              realTimeMonitoring={true}
              showSuggestions={true}
            />
          </div>
        </Modal>
      )}

      {activeFeaturePanel === 'data' && enableFeatures.dataManagement && (
        <Modal
          title="Data Management"
          open={true}
          onCancel={() => setActiveFeaturePanel(null)}
          footer={null}
          width="90%"
          style={{ top: 20 }}
          bodyStyle={{ padding: 0, height: '80vh', overflow: 'auto' }}
        >
          <div style={{ padding: '16px' }}>
            <DataManagementTools
              components={components}
              onDataChange={handleDataChange}
              onBindingCreate={handleBindingCreate}
              onBindingUpdate={handleBindingUpdate}
              onBindingDelete={handleBindingDelete}
              realTimeUpdates={true}
              showVisualization={true}
            />
          </div>
        </Modal>
      )}

      {activeFeaturePanel === 'export' && enableFeatures.enhancedExport && (
        <Modal
          title="Enhanced Code Export"
          open={true}
          onCancel={() => setActiveFeaturePanel(null)}
          footer={null}
          width="90%"
          style={{ top: 20 }}
          bodyStyle={{ padding: 0, height: '80vh', overflow: 'auto' }}
        >
          <div style={{ padding: '16px' }}>
            <EnhancedCodeExporter
              components={components}
              layouts={[]}
              theme={theme}
              onExport={handleEnhancedExport}
              onPreview={handleCodePreview}
            />
          </div>
        </Modal>
      )}

      {/* Loading Overlay */}
      {loading && (
        <LoadingOverlay>
          <Spin size="large" />
          <div className="loading-text">Loading App Builder...</div>
        </LoadingOverlay>
      )}

      {/* Development Tools */}
      {process.env.NODE_ENV === 'development' && (
        <div style={{
          position: 'fixed',
          bottom: theme.spacing[4],
          left: theme.spacing[4],
          zIndex: theme.zIndex.tooltip,
        }}>
          <Alert
            message={
              `Components: ${components.length} | ` +
              `Modified: ${isModified ? 'Yes' : 'No'} | ` +
              `Connected: ${websocketConnected ? 'Yes' : 'No'} | ` +
              `Active Panel: ${activeFeaturePanel ? activeFeaturePanel.charAt(0).toUpperCase() + activeFeaturePanel.slice(1) : 'None'} | ` +
              `Features: ${Object.values(enableFeatures).filter(Boolean).length}/${Object.keys(enableFeatures).length} enabled`
            }
            type="info"
            size="small"
            showIcon={false}
          />
        </div>
      )}
    </IntegratedContainer>
  );
}
